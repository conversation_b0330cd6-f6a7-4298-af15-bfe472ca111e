import * as cdk from "aws-cdk-lib";
import {
  UserPool,
  UserPoolClient,
  OAuthScope,
  UserPoolClientIdentityProvider,
  UserPoolGroup,
  UserPoolDomain,
} from "aws-cdk-lib/aws-cognito";
import { Construct } from "constructs";

export interface FrontendStackProps extends cdk.StackProps {
  applicationName: string;
}

export class FrontendStack extends cdk.Stack {
  private userPool: UserPool;
  private userPoolClient: UserPoolClient;
  private userPoolDomain: UserPoolDomain;

  constructor(scope: Construct, id: string, props?: FrontendStackProps) {
    super(scope, id, props);

    this.userPool = new UserPool(this, "EAPUserPool", {
      userPoolName: `${props?.applicationName}-userpool`,
      selfSignUpEnabled: true,
      signInAliases: {
        email: true,
      },
      signInCaseSensitive: false,
    });

    this.userPoolDomain = new UserPoolDomain(this, "EAPUserPoolDomain", {
      userPool: this.userPool,
      cognitoDomain: {
        domainPrefix: `${props?.applicationName}-auth`,
      },
    });

    this.userPoolClient = new UserPoolClient(this, "EAPUserPoolClient", {
      userPool: this.userPool,
      userPoolClientName: `${props?.applicationName}-userpool-client`,
      oAuth: {
        flows: {
          authorizationCodeGrant: true,
          implicitCodeGrant: false,
          clientCredentials: false,
        },
        scopes: [OAuthScope.OPENID, OAuthScope.EMAIL, OAuthScope.PROFILE],
        callbackUrls: ["http://localhost:3000/auth/callback"],
        logoutUrls: ["http://localhost:3000/auth/logout"],
      },
      supportedIdentityProviders: [UserPoolClientIdentityProvider.COGNITO],
      generateSecret: false,
      preventUserExistenceErrors: true,
    });

    new UserPoolGroup(this, "EAPUserPoolAdminGroup", {
      userPool: this.userPool,
      groupName: "admin",
    });
  }

  public getUserPool(): UserPool {
    return this.userPool;
  }

  public getUserPoolClient(): UserPoolClient {
    return this.userPoolClient;
  }

  public getUserPoolDomain(): UserPoolDomain {
    return this.userPoolDomain;
  }
}
