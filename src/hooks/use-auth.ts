import {
  getCurrentUser,
  fetchAuthSession,
  type AuthUser,
} from "aws-amplify/auth";
import { useEffect, useState } from "react";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface User extends AuthUser {}

const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    fetchUser();
  }, []);

  // TODO: Implement syncUserInfo

  const fetchUser = async () => {
    try {
      setIsLoading(true);
      const session = await fetchAuthSession();
      console.log(session);
      if (!session.userSub) {
        setIsLoading(false);
        return;
      }

      const user = await getCurrentUser();
      setUser(user);
    } catch (error) {
      console.error("Error fetching user:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return { user, isLoading };
};

export default useAuth;
